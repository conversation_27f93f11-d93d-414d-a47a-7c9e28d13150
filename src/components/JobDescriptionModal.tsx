import { useState, useEffect } from 'react';
import { jobService } from '../services/jobService';
import type { Job } from '../types';

interface JobDescriptionModalProps {
  job: Job;
  isOpen: boolean;
  onClose: () => void;
}

export function JobDescriptionModal({ job, isOpen, onClose }: JobDescriptionModalProps) {
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasDescription, setHasDescription] = useState(false);

  useEffect(() => {
    const loadDescription = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await jobService.getDescription(job.id);
        setDescription(response.description);
        setHasDescription(true);
      } catch (err: unknown) {
        if (err && typeof err === 'object' && 'response' in err) {
          const axiosError = err as { response?: { status?: number } };
          if (axiosError.response?.status === 404) {
            setDescription('');
            setHasDescription(false);
          } else {
            if (err instanceof Error && err.name === 'CORSError') {
              setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
            } else {
              setError('Failed to load job description');
            }
            console.error('Error loading description:', err);
          }
        } else {
          if (err instanceof Error && err.name === 'CORSError') {
            setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
          } else {
            setError('Failed to load job description');
          }
          console.error('Error loading description:', err);
        }
      } finally {
        setLoading(false);
      }
    };

    if (isOpen && job) {
      loadDescription();
    }
  }, [isOpen, job]);

  const handleSave = async () => {
    if (!description.trim()) {
      setError('Description cannot be empty');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      
      if (hasDescription) {
        await jobService.updateDescription(job.id, description);
      } else {
        await jobService.createDescription(job.id, description);
        setHasDescription(true);
      }
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to save job description');
      }
      console.error('Error saving description:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete this job description?')) {
      return;
    }

    try {
      setSaving(true);
      setError(null);
      await jobService.deleteDescription(job.id);
      setDescription('');
      setHasDescription(false);
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to delete job description');
      }
      console.error('Error deleting description:', err);
    } finally {
      setSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold">
            Job Description - {job.title}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">
              <strong>Company:</strong> {job.company}
            </p>
            {job.location && (
              <p className="text-sm text-gray-600 mb-2">
                <strong>Location:</strong> {job.location}
              </p>
            )}
          </div>

          {loading ? (
            <div className="text-center py-8">
              <div className="text-gray-500">Loading description...</div>
            </div>
          ) : (
            <div>
              <label htmlFor="description" className="mb-2">
                Job Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={12}
                placeholder="Enter the job description here..."
              />
            </div>
          )}
        </div>

        <div className="flex justify-between items-center p-6 border-t border-gray-200">
          <div>
            {hasDescription && (
              <button
                onClick={handleDelete}
                disabled={saving}
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {saving ? 'Deleting...' : 'Delete Description'}
              </button>
            )}
          </div>
          <div className="space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Close
            </button>
            <button
              onClick={handleSave}
              disabled={saving || !description.trim()}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? 'Saving...' : (hasDescription ? 'Update Description' : 'Save Description')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}