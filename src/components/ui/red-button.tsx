import type { ComponentProps } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

function RedButton({ 
  variant = 'default',
  className,
  ...props 
}: ComponentProps<typeof Button>) {
  // Red styling for default variant
  const redButtonClass = variant === 'default'
    ? 'bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700'
    : '';

  return (
    <Button
      variant={variant}
      className={cn(redButtonClass, className)}
      {...props}
    />
  );
}

export { RedButton }