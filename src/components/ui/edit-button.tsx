import type { ComponentProps } from 'react';
import { Pencil } from 'lucide-react';
import { BlueButton } from '@/components/ui/blue-button';
import { cn } from '@/lib/utils';

interface EditButtonProps extends ComponentProps<typeof BlueButton> {
  iconSize?: 'sm' | 'md' | 'lg';
}

function EditButton({ 
  iconSize = 'md', 
  size = 'icon',
  variant = 'default',
  className,
  ...props 
}: EditButtonProps) {
  const iconSizeClasses = {
    sm: 'size-3',
    md: 'size-4', 
    lg: 'size-5'
  };

  return (
    <BlueButton
      size={size}
      variant={variant}
      className={cn(className)}
      {...props}
    >
      <Pencil className={iconSizeClasses[iconSize]} />
    </BlueButton>
  );
}

export { EditButton }