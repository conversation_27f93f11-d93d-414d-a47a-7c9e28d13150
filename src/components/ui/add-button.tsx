import type { ComponentProps } from 'react';
import { Plus } from 'lucide-react';
import { GreenButton } from '@/components/ui/green-button';
import { cn } from '@/lib/utils';

interface AddButtonProps extends ComponentProps<typeof GreenButton> {
  iconSize?: 'sm' | 'md' | 'lg';
}

function AddButton({ 
  iconSize = 'md', 
  size = 'icon',
  variant = 'default',
  className,
  ...props 
}: AddButtonProps) {
  const iconSizeClasses = {
    sm: 'size-3',
    md: 'size-4', 
    lg: 'size-5'
  };

  return (
    <GreenButton
      size={size}
      variant={variant}
      className={cn(className)}
      {...props}
    >
      <Plus className={cn(iconSizeClasses[iconSize], 'stroke-[2.5]')} />
    </GreenButton>
  );
}

export { AddButton }