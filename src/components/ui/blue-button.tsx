import type { ComponentProps } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

function BlueButton({ 
  variant = 'default',
  className,
  ...props 
}: ComponentProps<typeof Button>) {
  // Blue styling for default variant
  const blueButtonClass = variant === 'default'
    ? 'bg-blue-500 hover:bg-blue-600 text-white border-blue-500 hover:border-blue-600'
    : '';

  return (
    <Button
      variant={variant}
      className={cn(blueButtonClass, className)}
      {...props}
    />
  );
}

export { BlueButton }