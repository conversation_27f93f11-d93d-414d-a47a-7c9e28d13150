import type { ComponentProps } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

function GreenButton({ 
  variant = 'default',
  className,
  ...props 
}: ComponentProps<typeof Button>) {
  // Green styling for default variant
  const greenButtonClass = variant === 'default' 
    ? 'bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700' 
    : '';

  return (
    <Button
      variant={variant}
      className={cn(greenButtonClass, className)}
      {...props}
    />
  );
}

export { GreenButton }