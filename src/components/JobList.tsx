import { useState, useEffect } from 'react';
import { jobService } from '../services/jobService';
import { EditButton } from './ui/edit-button';
import type { Job } from '../types';

interface JobListProps {
  onEditJob: (job: Job) => void;
  onViewDescription: (job: Job) => void;
  refreshTrigger: number;
}

export function JobList({ onEditJob, onViewDescription, refreshTrigger }: JobListProps) {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await jobService.getAll();
      setJobs(response.jobs);
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to load jobs');
      }
      console.error('Error loading jobs:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
  }, [refreshTrigger]);

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this job?')) {
      return;
    }

    try {
      await jobService.delete(id);
      setJobs(jobs.filter(job => job.id !== id));
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to delete job');
      }
      console.error('Error deleting job:', err);
    }
  };

  const handleProcess = async (id: string) => {
    try {
      const response = await jobService.processJob(id);
      alert(JSON.stringify(response, null, 2));
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to process job');
      }
      console.error('Error processing job:', err);
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString();
  };

  if (loading) {
    return <div className="text-center py-4">Loading jobs...</div>;
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {error}
        <button
          onClick={loadJobs}
          className="ml-2 text-red-800 underline hover:no-underline"
        >
          Retry
        </button>
      </div>
    );
  }

  if (jobs.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        No jobs found. Create your first job above.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold mb-4">Your Jobs ({jobs.length})</h2>
      {jobs.map((job) => (
        <div key={job.id} className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm">
          <div className="flex justify-between items-start mb-2">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
              <p className="text-gray-600">{job.company}</p>
              {job.location && <p className="text-gray-500 text-sm">{job.location}</p>}
            </div>
            <div className="flex space-x-2">
              <EditButton
                onClick={() => onEditJob(job)}
                title="Edit job"
              />
              <button
                onClick={() => onViewDescription(job)}
                className="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
              >
                Description
              </button>
              <button
                onClick={() => handleProcess(job.id)}
                className="px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
              >
                Process
              </button>
              <button
                onClick={() => handleDelete(job.id)}
                className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
              >
                Delete
              </button>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            <p>
              {formatDate(job.startDate)} - {job.isCurrent ? 'Present' : job.endDate ? formatDate(job.endDate) : 'N/A'}
            </p>
          </div>
          {job.description && (
            <p className="mt-2 text-gray-700 text-sm line-clamp-3">{job.description}</p>
          )}
        </div>
      ))}
    </div>
  );
}