import { Link } from 'react-router-dom'
import { authService } from '@/services/authService'

export function Header() {
  const isAuthenticated = authService.isAuthenticated();
  const user = authService.getCurrentUser();

  const handleLogout = () => {
    authService.logout();
    window.location.href = '/';
  };

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <Link to="/">
            <h1>Impact Resume</h1>
          </Link>
        </div>
        <nav className="nav">
          <ul className="nav-list">
            <li><Link to="/" className="nav-link">Home</Link></li>
            <li><Link to="/resume" className="nav-link">Resume</Link></li>
            <li><Link to="/profile" className="nav-link">Profile</Link></li>
            {isAuthenticated ? (
              <li><button onClick={handleLogout} className="nav-link">Logout</button></li>
            ) : (
              <li><Link to="/login" className="nav-link">Login</Link></li>
            )}
          </ul>
        </nav>
      </div>
    </header>
  )
}