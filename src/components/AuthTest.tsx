import { useAuth } from '@/contexts/AuthContext';

export function AuthTest() {
  const { isAuthenticated, user, login, logout } = useAuth();

  const handleTestLogin = () => {
    const testUser = {
      id: 'test-123',
      email: '<EMAIL>',
      name: 'Test User',
      provider: 'google' as const
    };
    const testToken = 'test-jwt-token';
    
    login(testUser, testToken);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
      <h3>Auth Context Test</h3>
      <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
      <p>User: {user ? `${user.name} (${user.email})` : 'None'}</p>
      
      <div style={{ marginTop: '10px' }}>
        <button onClick={handleTestLogin} style={{ marginRight: '10px' }}>
          Test Login
        </button>
        <button onClick={logout}>
          Test Logout
        </button>
      </div>
    </div>
  );
}
