import { useState, useEffect } from 'react';
import { jobService } from '../services/jobService';
import type { Job, JobCreateRequest } from '../types';

interface JobFormProps {
  job?: Job;
  onSuccess: () => void;
  onCancel: () => void;
}

export function JobForm({ job, onSuccess, onCancel }: JobFormProps) {
  const [formData, setFormData] = useState<JobCreateRequest>({
    title: '',
    company: '',
    location: '',
    description: '',
    startDate: '',
    endDate: '',
    isCurrent: false,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (job) {
      setFormData({
        title: job.title,
        company: job.company,
        location: job.location || '',
        description: job.description,
        startDate: job.startDate,
        endDate: job.endDate || '',
        isCurrent: job.isCurrent,
      });
    }
  }, [job]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.company.trim()) {
      setError('Title and company are required');
      return;
    }

    if (!formData.startDate.trim()) {
      setError('Start date is required');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const submitData = {
        ...formData,
        endDate: formData.isCurrent || !formData.endDate
          ? undefined 
          : formData.endDate
      };

      if (job) {
        // For updates, we would need an update endpoint in the service
        // For now, we'll show an error since update isn't implemented
        setError('Job updates not yet implemented in the API');
      } else {
        await jobService.create(submitData);
        onSuccess();
      }
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to save job');
      }
      console.error('Error saving job:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDateForInput = (date?: string) => {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h2 className="text-xl font-semibold mb-4">
        {job ? 'Edit Job' : 'Create New Job'}
      </h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="title" className="mb-1">
            Job Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            required
            placeholder="e.g. Software Engineer"
          />
        </div>

        <div>
          <label htmlFor="company" className="mb-1">
            Company *
          </label>
          <input
            type="text"
            id="company"
            name="company"
            value={formData.company}
            onChange={handleInputChange}
            required
            placeholder="e.g. Acme Corp"
          />
        </div>

        <div>
          <label htmlFor="location" className="mb-1">
            Location
          </label>
          <input
            type="text"
            id="location"
            name="location"
            value={formData.location}
            onChange={handleInputChange}
            placeholder="e.g. San Francisco, CA"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="startDate" className="mb-1">
              Start Date *
            </label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              value={formatDateForInput(formData.startDate)}
              onChange={handleInputChange}
              required
              />
          </div>

          <div>
            <label htmlFor="endDate" className="mb-1">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              value={formatDateForInput(formData.endDate)}
              onChange={handleInputChange}
              disabled={formData.isCurrent}
            />
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isCurrent"
            name="isCurrent"
            checked={formData.isCurrent}
            onChange={handleInputChange}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isCurrent" className="inline-label ml-2 block text-sm text-gray-700">
            This is my current job
          </label>
        </div>

        <div>
          <label htmlFor="description" className="mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
            placeholder="Describe your role, responsibilities, and achievements..."
          />
        </div>

        <div className="flex space-x-3 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : (job ? 'Update Job' : 'Create Job')}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}