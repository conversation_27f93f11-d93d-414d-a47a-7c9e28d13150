export interface Job {
  id: string;
  title: string;
  company: string;
  location?: string;
  description: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface JobCreateRequest {
  title: string;
  company: string;
  location?: string;
  description: string;
  startDate: string;
  endDate?: string;
  isCurrent: boolean;
}

export interface JobListResponse {
  jobs: Job[];
  total: number;
}

export interface JobProcessRequest {
  jobIds: string[];
  model?: string;
}

export interface ProcessedJob {
  jobId: string;
  bulletPoints: string[];
  model: string;
  processedAt: string;
  tokensUsed: number;
}

export interface JobProcessResponse {
  processedJobs: ProcessedJob[];
  total: number;
}

export interface JobDescriptionRequest {
  description: string;
}

export interface JobDescriptionResponse {
  id: string;
  description: string;
}