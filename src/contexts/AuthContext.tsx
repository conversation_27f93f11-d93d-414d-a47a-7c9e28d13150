import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService, AuthUser } from '@/services/authService';

interface AuthContextType {
  user: AuthUser | null;
  isAuthenticated: boolean;
  login: (user: AuthUser, token: string) => void;
  logout: () => void;
  refreshAuthState: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Function to refresh auth state from localStorage
  const refreshAuthState = () => {
    const currentUser = authService.getCurrentUser();
    const authenticated = authService.isAuthenticated();
    
    setUser(currentUser);
    setIsAuthenticated(authenticated);
    
    console.log('Auth state refreshed:', { 
      authenticated, 
      userEmail: currentUser?.email 
    });
  };

  // Initialize auth state on mount
  useEffect(() => {
    refreshAuthState();
  }, []);

  // Listen for storage changes (in case auth state changes in another tab)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'authToken' || e.key === 'user') {
        console.log('Storage change detected, refreshing auth state');
        refreshAuthState();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const login = (userData: AuthUser, token: string) => {
    // Store in localStorage (this is already done by authService, but we ensure it here)
    localStorage.setItem('authToken', token);
    localStorage.setItem('user', JSON.stringify(userData));
    
    // Update context state
    setUser(userData);
    setIsAuthenticated(true);
    
    console.log('User logged in via context:', userData.email);
  };

  const logout = () => {
    // Clear localStorage
    authService.logout();
    
    // Update context state
    setUser(null);
    setIsAuthenticated(false);
    
    console.log('User logged out via context');
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    login,
    logout,
    refreshAuthState,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
