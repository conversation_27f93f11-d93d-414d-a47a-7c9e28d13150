import axios from 'axios';

const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1';

export const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false, // Set to true if you need to send cookies/auth headers
});

apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle CORS errors
    if (!error.response && error.request) {
      console.error('CORS or Network Error:', error.message);
      const corsError = new Error(
        'Unable to connect to the API. This might be due to CORS configuration or network issues.'
      );
      corsError.name = 'CORSError';
      return Promise.reject(corsError);
    }

    // Handle authentication errors
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
    }
    
    return Promise.reject(error);
  }
);