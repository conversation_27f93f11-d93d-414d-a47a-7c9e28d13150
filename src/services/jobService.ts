import { apiClient } from './api';
import type {
  Job,
  JobCreateRequest,
  JobListResponse,
  JobProcessRequest,
  JobProcessResponse,
  ProcessedJob,
  JobDescriptionResponse,
  SuccessResponse
} from '../types';

export const jobService = {
  // Get all jobs
  getAll: async (): Promise<JobListResponse> => {
    const response = await apiClient.get<JobListResponse>('/jobs');
    return response.data;
  },

  // Create a new job
  create: async (jobData: JobCreateRequest): Promise<Job> => {
    const response = await apiClient.post<Job>('/jobs', jobData);
    return response.data;
  },

  // Delete a job
  delete: async (id: string): Promise<SuccessResponse> => {
    const response = await apiClient.delete<SuccessResponse>(`/jobs/${id}`);
    return response.data;
  },

  // Job description operations
  getDescription: async (id: string): Promise<JobDescriptionResponse> => {
    const response = await apiClient.get<JobDescriptionResponse>(`/jobs/${id}/description`);
    return response.data;
  },

  createDescription: async (id: string, description: string): Promise<SuccessResponse> => {
    const response = await apiClient.post<SuccessResponse>(
      `/jobs/${id}/description`,
      { description }
    );
    return response.data;
  },

  updateDescription: async (id: string, description: string): Promise<SuccessResponse> => {
    const response = await apiClient.put<SuccessResponse>(
      `/jobs/${id}/description`,
      { description }
    );
    return response.data;
  },

  deleteDescription: async (id: string): Promise<SuccessResponse> => {
    const response = await apiClient.delete<SuccessResponse>(`/jobs/${id}/description`);
    return response.data;
  },

  // AI processing operations
  processJob: async (id: string): Promise<ProcessedJob> => {
    const response = await apiClient.post<ProcessedJob>(`/jobs/${id}/process`);
    return response.data;
  },

  processMultipleJobs: async (jobProcessRequest: JobProcessRequest): Promise<JobProcessResponse> => {
    const response = await apiClient.post<JobProcessResponse>('/jobs/process', jobProcessRequest);
    return response.data;
  },
};