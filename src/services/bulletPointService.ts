import type { ProcessedJob } from '../types';

export const bulletPointService = {
  // Extract bullet points from processed jobs
  extractBulletPoints: (processedJob: ProcessedJob): string[] => {
    return processedJob.bulletPoints || [];
  },

  // Extract all bullet points from multiple processed jobs
  extractAllBulletPoints: (processedJobs: ProcessedJob[]): string[] => {
    return processedJobs.flatMap(job => job.bulletPoints || []);
  },

  // Group bullet points by job ID
  groupBulletPointsByJob: (processedJobs: ProcessedJob[]): Record<string, string[]> => {
    return processedJobs.reduce((acc, job) => {
      acc[job.jobId] = job.bulletPoints || [];
      return acc;
    }, {} as Record<string, string[]>);
  },

  // Filter bullet points by keyword
  filterBulletPoints: (bulletPoints: string[], keyword: string): string[] => {
    const lowerKeyword = keyword.toLowerCase();
    return bulletPoints.filter(point => 
      point.toLowerCase().includes(lowerKeyword)
    );
  },

  // Count total bullet points
  countBulletPoints: (processedJobs: ProcessedJob[]): number => {
    return processedJobs.reduce((total, job) => total + (job.bulletPoints?.length || 0), 0);
  },

  // Format bullet points for display
  formatBulletPoints: (bulletPoints: string[]): string => {
    return bulletPoints.map(point => `• ${point}`).join('\n');
  },

  // Convert bullet points to markdown list
  toBulletMarkdown: (bulletPoints: string[]): string => {
    return bulletPoints.map(point => `- ${point}`).join('\n');
  },
};