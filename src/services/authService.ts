import { apiClient } from './api';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  provider: 'google' | 'github' | 'linkedin';
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
}

const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:8080';
export const authService = {
  // OAuth login URLs - these redirect to the provider's auth page
  // Backend now handles redirect_uri configuration via FRONTEND_URL environment variable
  getGoogleLoginUrl: () => {
    const loginUrl = `${baseUrl}/auth/google/login`;
    console.log('Generating Google login URL:', { baseUrl, loginUrl });
    return loginUrl;
  },

  getGitHubLoginUrl: () => {
    const loginUrl = `${baseUrl}/auth/github/login`;
    console.log('Generating GitHub login URL:', { baseUrl, loginUrl });
    return loginUrl;
  },

  getLinkedInLoginUrl: () => {
    const loginUrl = `${baseUrl}/auth/linkedin/login`;
    console.log('Generating LinkedIn login URL:', { baseUrl, loginUrl });
    return loginUrl;
  },

  // Handle new token-based OAuth callback (backend redirects with JWT token)
  handleTokenCallback: async (token: string, provider: string): Promise<AuthResponse> => {
    console.log('Handling token-based OAuth callback:', { provider, tokenLength: token.length });

    try {
      // Validate the token format (basic JWT structure check)
      if (!token || typeof token !== 'string') {
        throw new Error('Invalid token received from OAuth callback');
      }

      // Basic JWT format validation (should have 3 parts separated by dots)
      const tokenParts = token.split('.');
      if (tokenParts.length !== 3) {
        throw new Error('Invalid JWT token format received');
      }

      // Store the token first
      localStorage.setItem('authToken', token);

      // Try to decode user info from JWT payload (without verification - just for display)
      let user: AuthUser;
      try {
        const payload = JSON.parse(atob(tokenParts[1]));
        console.log('JWT payload decoded:', {
          hasEmail: !!payload.email,
          hasName: !!payload.name,
          hasId: !!payload.id || !!payload.sub,
          provider: payload.provider || provider
        });

        // Create user object from JWT payload
        user = {
          id: payload.id || payload.sub || '',
          email: payload.email || '',
          name: payload.name || payload.full_name || '',
          avatar: payload.avatar || payload.picture,
          provider: (payload.provider || provider) as 'google' | 'github' | 'linkedin'
        };

        // Validate required user fields
        if (!user.email) {
          throw new Error('Email address is required but was not found in the authentication token.');
        }

        if (!user.id) {
          throw new Error('User ID is required but was not found in the authentication token.');
        }

      } catch (decodeError) {
        console.error('Failed to decode JWT payload:', decodeError);

        // Fallback: Try to get user info from API
        try {
          console.log('Attempting to fetch user info from API...');
          const response = await apiClient.get('/auth/me');
          user = response.data;

          if (!user || !user.email) {
            throw new Error('Invalid user data received from API');
          }
        } catch (apiError) {
          console.error('Failed to fetch user info from API:', apiError);
          throw new Error('Unable to retrieve user information. Please try logging in again.');
        }
      }

      console.log('User data validation passed:', {
        hasEmail: !!user.email,
        hasId: !!user.id,
        provider: user.provider
      });

      // Store user data
      localStorage.setItem('user', JSON.stringify(user));

      console.log('Authentication data stored successfully');

      return { user, token };
    } catch (error) {
      console.error('Token callback error:', error);

      // Clear any existing auth data on error
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');

      if (error instanceof Error) {
        throw error;
      } else {
        throw new Error('Authentication failed. Please try again.');
      }
    }
  },

  // Legacy: Handle old code-based OAuth callback (kept for backward compatibility)
  handleOAuthCallback: async (code: string, provider: string): Promise<AuthResponse> => {
    console.log('Handling legacy OAuth callback:', { provider, codeLength: code.length });
    console.warn('This method is deprecated. Backend should use token-based redirects.');

    try {
      const response = await apiClient.post(`/auth/${provider}/callback`, {
        code,
      });

      const { user, token } = response.data;

      if (!user || !token) {
        throw new Error('Invalid response from authentication server: missing user or token');
      }

      // Store token in localStorage
      localStorage.setItem('authToken', token);
      localStorage.setItem('user', JSON.stringify(user));

      return response.data;
    } catch (error) {
      console.error('Legacy OAuth callback error:', error);
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      throw error instanceof Error ? error : new Error('Authentication failed. Please try again.');
    }
  },

  // Get current user from localStorage
  getCurrentUser: (): AuthUser | null => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // Logout
  logout: () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  },

  // Refresh token if needed
  refreshToken: async (): Promise<string | null> => {
    try {
      const response = await apiClient.post('/auth/refresh');
      const { token } = response.data;
      localStorage.setItem('authToken', token);
      return token;
    } catch (error) {
      console.error('Token refresh failed:', error);
      authService.logout();
      return null;
    }
  },
};