import { useSearchParams, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { authService } from '@/services/authService';

export function DebugPage() {
  const [searchParams] = useSearchParams();
  const location = useLocation();

  const allParams = Object.fromEntries(searchParams.entries());
  const isAuthenticated = authService.isAuthenticated();
  const currentUser = authService.getCurrentUser();

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>OAuth Debug Information</CardTitle>
          <CardDescription>
            This page helps debug OAuth authentication issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Current URL:</h3>
            <code className="bg-gray-100 p-2 rounded block text-sm">
              {window.location.href}
            </code>
          </div>

          <div>
            <h3 className="font-semibold mb-2">URL Parameters:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(allParams, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Location Object:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify({
                pathname: location.pathname,
                search: location.search,
                hash: location.hash,
                state: location.state
              }, null, 2)}
            </pre>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Authentication Status:</h3>
            <div className="space-y-2">
              <p>Is Authenticated: <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                {isAuthenticated ? 'Yes' : 'No'}
              </span></p>
              
              <div>
                <p className="font-medium">Current User:</p>
                <pre className="bg-gray-100 p-2 rounded text-sm">
                  {currentUser ? JSON.stringify(currentUser, null, 2) : 'null'}
                </pre>
              </div>

              <div>
                <p className="font-medium">LocalStorage Auth Token:</p>
                <code className="bg-gray-100 p-2 rounded block text-sm">
                  {localStorage.getItem('authToken') ? 
                    localStorage.getItem('authToken')?.substring(0, 20) + '...' : 
                    'null'
                  }
                </code>
              </div>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Environment Variables:</h3>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify({
                VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
                VITE_BASE_URL: import.meta.env.VITE_BASE_URL,
                VITE_NODE_ENV: import.meta.env.VITE_NODE_ENV
              }, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
