import { useEffect, useCallback, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { authService } from '@/services/authService';
import { useAuth } from '@/contexts/AuthContext';

export function LoginPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isProcessingAuth, setIsProcessingAuth] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  const handleOAuthCallback = useCallback(async (token: string, provider: string) => {
    console.log('OAuth callback received:', { tokenLength: token.length, provider });
    setIsProcessingAuth(true);
    setAuthError(null);

    try {
      // Store the JWT token directly (no API call needed since backend already processed OAuth)
      await authService.handleTokenCallback(token, provider);
      console.log('OAuth token stored successfully for provider:', provider);

      // Small delay to ensure localStorage is updated
      setTimeout(() => {
        navigate('/', { replace: true });
      }, 100);
    } catch (error) {
      console.error('OAuth token processing failed:', error);
      setAuthError(error instanceof Error ? error.message : 'Authentication failed');
      // Remove callback params from URL to show login form again
      navigate('/login', { replace: true });
    } finally {
      setIsProcessingAuth(false);
    }
  }, [navigate]);

  useEffect(() => {
    console.log('LoginPage useEffect - Current URL params:', {
      token: searchParams.get('token') ? 'Present (length: ' + searchParams.get('token')!.length + ')' : 'Missing',
      provider: searchParams.get('provider'),
      error: searchParams.get('error'),
      allParams: Object.fromEntries(searchParams.entries())
    });

    // Check if already authenticated
    if (authService.isAuthenticated()) {
      console.log('User already authenticated, redirecting to home');
      navigate('/', { replace: true });
      return;
    }

    // Handle OAuth callback with new token-based flow
    const token = searchParams.get('token');
    const provider = searchParams.get('provider');
    const error = searchParams.get('error');

    // Check for OAuth errors first
    if (error) {
      console.error('OAuth error received:', error);
      setAuthError(`OAuth error: ${error}`);
      return;
    }

    // Handle the new token-based callback
    if (token && provider) {
      console.log('Token-based OAuth callback detected');
      handleOAuthCallback(token, provider);
      return;
    }

    // Legacy support: Handle old code-based flow (in case backend still sends codes)
    const code = searchParams.get('code');
    if (code && provider) {
      console.warn('Legacy code-based OAuth callback detected. This should not happen with the new backend flow.');
      setAuthError('Received legacy OAuth callback format. Please contact support if this persists.');
      return;
    }

    // If we have a token but no provider
    if (token && !provider) {
      console.error('Token received but no provider specified');
      setAuthError('OAuth callback missing provider information. Please try logging in again.');
      return;
    }

    // If we have neither token nor code, this is just a normal login page visit
    if (!token && !code) {
      console.log('Normal login page visit (no OAuth callback parameters)');
    }
  }, [navigate, searchParams, handleOAuthCallback]);

  const handleGoogleLogin = () => {
    window.location.href = authService.getGoogleLoginUrl();
  };

  const handleGitHubLogin = () => {
    window.location.href = authService.getGitHubLoginUrl();
  };

  const handleLinkedInLogin = () => {
    window.location.href = authService.getLinkedInLoginUrl();
  };

  // Show loading state during OAuth processing
  if (isProcessingAuth) {
    return (
      <div className="min-h-auto flex justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card>
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold text-center">
                Completing Sign In...
              </CardTitle>
              <CardDescription className="text-center">
                Processing your authentication token
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="text-sm text-gray-600">Validating your credentials...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-auto flex justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              Sign in to Impact Resume
            </CardTitle>
            <CardDescription className="text-center">
              Choose your preferred authentication method
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {authError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{authError}</p>
              </div>
            )}
            <Button
              onClick={handleGoogleLogin}
              variant="outline"
              className="w-full h-12 flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </Button>

            <Button
              onClick={handleGitHubLogin}
              variant="outline"
              className="w-full h-12 flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
              </svg>
              Continue with GitHub
            </Button>

            <Button
              onClick={handleLinkedInLogin}
              variant="outline"
              className="w-full h-12 flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
              </svg>
              Continue with LinkedIn
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}