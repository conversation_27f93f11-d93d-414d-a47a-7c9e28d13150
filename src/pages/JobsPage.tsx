import { useState } from 'react';
import { JobList } from '../components/JobList';
import { JobForm } from '../components/JobForm';
import { JobDescriptionModal } from '../components/JobDescriptionModal';
import { AddButton } from '@/components/ui/add-button';
import type { Job } from '../types';

export function JobsPage() {
  const [showForm, setShowForm] = useState(false);
  const [editingJob, setEditingJob] = useState<Job | undefined>();
  const [selectedJob, setSelectedJob] = useState<Job | undefined>();
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleCreateNew = () => {
    setEditingJob(undefined);
    setShowForm(true);
  };

  const handleEditJob = (job: Job) => {
    setEditingJob(job);
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingJob(undefined);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingJob(undefined);
  };

  const handleViewDescription = (job: Job) => {
    setSelectedJob(job);
  };

  const handleCloseDescriptionModal = () => {
    setSelectedJob(undefined);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Job Management</h1>
          <p className="text-gray-600">
            Manage your work experience, create job descriptions, and track your career history.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Form */}
          <div>
            {showForm ? (
              <JobForm
                job={editingJob}
                onSuccess={handleFormSuccess}
                onCancel={handleFormCancel}
              />
            ) : (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h2 className="text-xl font-semibold mb-4">Add New Job</h2>
                <p className="text-gray-600 mb-4">
                  Start by creating a new job entry to track your work experience.
                </p>
                <AddButton
                  onClick={handleCreateNew}
                >
                  Create New Job
                </AddButton>
              </div>
            )}
          </div>

          {/* Right Column - Job List */}
          <div>
            <JobList
              onEditJob={handleEditJob}
              onViewDescription={handleViewDescription}
              refreshTrigger={refreshTrigger}
            />
          </div>
        </div>

        {/* Job Description Modal */}
        {selectedJob && (
          <JobDescriptionModal
            job={selectedJob}
            isOpen={!!selectedJob}
            onClose={handleCloseDescriptionModal}
          />
        )}
      </div>
    </div>
  );
}