import { useState, useEffect } from 'react';
import { jobService } from '../services/jobService';
import { JobForm } from '../components/JobForm';
import type { Job } from '../types';
import {EditButton} from "@/components/ui/edit-button.tsx";
import {AddButton} from "@/components/ui/add-button";

export function ResumePage() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingJob, setEditingJob] = useState<Job | undefined>();
  const [showJobForm, setShowJobForm] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const loadJobs = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await jobService.getAll();
      setJobs(response.jobs.sort((a, b) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime()));
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to load jobs');
      }
      console.error('Error loading jobs:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
  }, [refreshTrigger]);

  const handleEditJob = (job: Job) => {
    setEditingJob(job);
    setShowJobForm(true);
  };

  const handleAddJob = () => {
    setEditingJob(undefined);
    setShowJobForm(true);
  };

  const handleJobFormSuccess = () => {
    setShowJobForm(false);
    setEditingJob(undefined);
    setRefreshTrigger(prev => prev + 1);
  };

  const handleJobFormCancel = () => {
    setShowJobForm(false);
    setEditingJob(undefined);
  };

  const handleDeleteJob = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this job?')) {
      return;
    }

    try {
      await jobService.delete(id);
      setJobs(jobs.filter(job => job.id !== id));
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to delete job');
      }
      console.error('Error deleting job:', err);
    }
  };

  const handleProcessJob = async (id: string) => {
    try {
      const response = await jobService.processJob(id);
      alert(JSON.stringify(response, null, 2));
    } catch (err: unknown) {
      if (err instanceof Error && err.name === 'CORSError') {
        setError('Unable to connect to the API. Please check if the backend server is running and CORS is configured properly.');
      } else {
        setError('Failed to process job');
      }
      console.error('Error processing job:', err);
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="text-center py-8">Loading resume...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Resume</h1>
          <p className="text-gray-600">
            View and edit all sections of your resume in one place.
          </p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
            <button
              onClick={loadJobs}
              className="ml-2 text-red-800 underline hover:no-underline"
            >
              Retry
            </button>
          </div>
        )}

        <div className="space-y-6">
          {/* Personal Information Section */}
          <section className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Personal Information</h2>
              <EditButton
                onClick={() => setEditingSection(editingSection === 'personal' ? null : 'personal')}
              >
                {editingSection === 'personal' ? 'Cancel' : 'Edit'}
              </EditButton>
            </div>
            {editingSection === 'personal' ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Full Name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="email"
                    placeholder="Email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="tel"
                    placeholder="Phone"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="Location"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <textarea
                  placeholder="Professional Summary"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="flex space-x-2">
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div>
                <div className="text-gray-600 space-y-2">
                  <p className="text-lg font-medium text-gray-900">[Your Name]</p>
                  <p>[Your Email] • [Your Phone] • [Your Location]</p>
                  <p className="mt-3 text-gray-700">[Your professional summary will appear here]</p>
                </div>
              </div>
            )}
          </section>

          {/* Experience Section */}
          <section className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Work Experience</h2>
              <AddButton
                onClick={handleAddJob}
                title="Add job"
              />
            </div>
            
            {jobs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No work experience added yet.</p>
                <AddButton
                  onClick={handleAddJob}
                  variant="link"
                  size="default"
                  className="mt-2"
                >
                  Add your first job
                </AddButton>
              </div>
            ) : (
              <div className="space-y-4">
                {jobs.map((job) => (
                  <div key={job.id} className="border-l-4 border-blue-500 pl-4 pb-4">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                        <p className="text-gray-600 font-medium">{job.company}</p>
                        {job.location && <p className="text-gray-500 text-sm">{job.location}</p>}
                        <p className="text-gray-500 text-sm">
                          {formatDate(job.startDate)} - {job.isCurrent ? 'Present' : job.endDate ? formatDate(job.endDate) : 'N/A'}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <EditButton
                          onClick={() => handleEditJob(job)}
                          size="default"
                        >
                          Edit
                        </EditButton>
                        <button
                          onClick={() => handleProcessJob(job.id)}
                          className="px-3 py-1 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
                        >
                          Process
                        </button>
                        <button
                          onClick={() => handleDeleteJob(job.id)}
                          className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                    {job.description && (
                      <div className="mt-2 text-gray-700 text-sm whitespace-pre-wrap">
                        {job.description}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </section>

          {/* Education Section */}
          <section className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Education</h2>
              <EditButton
                onClick={() => setEditingSection(editingSection === 'education' ? null : 'education')}
                size="default"
              >
                {editingSection === 'education' ? 'Cancel' : 'Edit'}
              </EditButton>
            </div>
            {editingSection === 'education' ? (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    placeholder="Degree"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="School/University"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="Graduation Year"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    placeholder="GPA (optional)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">
                <p>[Your education details will appear here]</p>
              </div>
            )}
          </section>

          {/* Skills Section */}
          <section className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-900">Skills</h2>
              <EditButton
                onClick={() => setEditingSection(editingSection === 'skills' ? null : 'skills')}
                size="default"
              >
                {editingSection === 'skills' ? 'Cancel' : 'Edit'}
              </EditButton>
            </div>
            {editingSection === 'skills' ? (
              <div className="space-y-4">
                <textarea
                  placeholder="List your skills (e.g., JavaScript, React, Node.js, Python, etc.)"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="flex space-x-2">
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Save
                  </button>
                  <button
                    onClick={() => setEditingSection(null)}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">
                <p>[Your skills will appear here]</p>
              </div>
            )}
          </section>
        </div>

        {/* Job Form Modal */}
        {showJobForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <h2 className="text-xl font-semibold mb-4">
                  {editingJob ? 'Edit Job' : 'Add New Job'}
                </h2>
                <JobForm
                  job={editingJob}
                  onSuccess={handleJobFormSuccess}
                  onCancel={handleJobFormCancel}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}