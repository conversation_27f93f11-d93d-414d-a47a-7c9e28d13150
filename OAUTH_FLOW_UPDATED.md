# Updated OAuth Authentication Flow

## Overview
The frontend has been updated to work with the new backend OAuth flow that redirects with JWT tokens instead of returning JSON responses.

## New OAuth Flow

### 1. User Initiates Login
- User clicks on Google/GitHub/LinkedIn login button
- Frontend redirects to: `http://localhost:8080/auth/{provider}/login`
- Backend handles OAuth provider redirect configuration

### 2. OAuth Provider Authentication
- User authenticates with the OAuth provider (Google/GitHub/LinkedIn)
- OAuth provider redirects back to backend callback: `http://localhost:8080/auth/{provider}/callback`

### 3. Backend Processing (NEW)
- Backend processes OAuth response
- Creates or updates user in database
- Generates JWT token with user information
- **Redirects to frontend**: `http://localhost:5173/login?token={jwt_token}&provider={provider}`

### 4. Frontend Token Processing (UPDATED)
- Frontend receives redirect with `token` and `provider` parameters
- Extracts JWT token from URL parameters
- Decodes user information from JWT payload
- Stores token and user data in localStorage
- Redirects to home page

## Key Changes Made

### Frontend Updates

#### 1. LoginPage.tsx
- **NEW**: `handleTokenCallback()` method to process JWT tokens
- **UPDATED**: URL parameter handling to look for `token` instead of `code`
- **IMPROVED**: Error handling and loading states
- **ADDED**: JWT payload decoding for user information

#### 2. authService.ts
- **NEW**: `handleTokenCallback()` method for token-based authentication
- **UPDATED**: OAuth URL generation (removed manual redirect_uri)
- **IMPROVED**: JWT token validation and user data extraction
- **MAINTAINED**: Legacy `handleOAuthCallback()` for backward compatibility

#### 3. Environment Configuration
- **CLARIFIED**: `VITE_BASE_URL` is now used for OAuth redirects only
- **MAINTAINED**: `VITE_API_BASE_URL` for API calls

## Configuration Requirements

### Backend Configuration
Ensure your backend has:
```env
FRONTEND_URL=http://localhost:5173
```

### Frontend Configuration
```env
# API calls
VITE_API_BASE_URL=http://localhost:8080/api/v1
# OAuth redirects (matches backend base URL)
VITE_BASE_URL=http://localhost:8080
```

## Expected URL Flow

### Development
1. **Login initiation**: `http://localhost:5173/login` → Click button
2. **OAuth redirect**: `http://localhost:8080/auth/google/login`
3. **Provider auth**: `https://accounts.google.com/oauth/authorize?...`
4. **Backend callback**: `http://localhost:8080/auth/google/callback?code=...`
5. **Frontend redirect**: `http://localhost:5173/login?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&provider=google`
6. **Final redirect**: `http://localhost:5173/` (home page)

### Production
Replace `localhost:5173` with your production frontend domain and `localhost:8080` with your production backend domain.

## JWT Token Structure

The JWT token should contain user information in the payload:
```json
{
  "id": "user_id",
  "email": "<EMAIL>", 
  "name": "User Name",
  "avatar": "https://avatar-url.com/image.jpg",
  "provider": "google",
  "exp": **********,
  "iat": **********
}
```

## Error Handling

The frontend now handles these error scenarios:
- Invalid JWT token format
- Missing required user fields (email, name, id)
- OAuth provider errors
- Network/API errors
- Token decoding failures

## Testing the Flow

1. Start your backend server with the updated OAuth configuration
2. Start the frontend development server: `npm run dev`
3. Navigate to `http://localhost:5173/login`
4. Click any OAuth provider button
5. Complete authentication with the provider
6. Verify you're redirected to the home page
7. Check browser console for detailed logging

## Debugging

Enable detailed logging by checking the browser console. The updated flow logs:
- OAuth URL generation
- Token reception and validation
- JWT payload decoding
- User data extraction
- Authentication state changes

If authentication fails, check:
1. Backend `FRONTEND_URL` configuration
2. OAuth provider callback URL configuration
3. JWT token structure and required fields
4. Browser console for specific error messages
