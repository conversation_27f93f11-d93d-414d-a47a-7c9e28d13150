# Backend CORS Configuration Requirements

## Overview
The frontend application requires CORS (Cross-Origin Resource Sharing) to be properly configured on the backend API to allow communication between the frontend (typically running on `http://localhost:5173` during development) and the backend API.

## Required CORS Configuration

### Origins
Allow the following origins:
- **Development**: `http://localhost:5173` (Vite dev server default)
- **Development Alternative**: `http://localhost:3000` (if using different port)
- **Production**: Your production frontend domain

### Headers
Allow the following headers:
- `Content-Type`
- `Authorization` (for JWT tokens stored in localStorage)
- `Accept`
- `Origin`
- `X-Requested-With`

### Methods
Allow the following HTTP methods:
- `GET`
- `POST`
- `PUT`
- `DELETE`
- `OPTIONS` (for preflight requests)

### Credentials
- Set to `false` unless you need to send cookies or authentication headers
- If you need authentication via cookies, set to `true` and update the frontend `withCredentials` setting

## Example Backend Configuration

### Go (Gin framework)
```go
import "github.com/gin-contrib/cors"

func setupCORS() gin.HandlerFunc {
    config := cors.DefaultConfig()
    config.AllowOrigins = []string{
        "http://localhost:5173", // Vite dev server
        "http://localhost:3000", // Alternative dev server
        "https://yourdomain.com", // Production domain
    }
    config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
    config.AllowHeaders = []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"}
    config.AllowCredentials = false // Set to true if using cookies
    
    return cors.New(config)
}

// In your main function or router setup:
r.Use(setupCORS())
```

### Node.js (Express)
```javascript
const cors = require('cors');

const corsOptions = {
    origin: [
        'http://localhost:5173',
        'http://localhost:3000',
        'https://yourdomain.com'
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Origin', 'Content-Type', 'Accept', 'Authorization', 'X-Requested-With'],
    credentials: false // Set to true if using cookies
};

app.use(cors(corsOptions));
```

## Testing CORS Configuration

1. Start your backend server
2. Start the frontend with `npm run dev`
3. Open browser developer tools
4. Try to create a new job or load jobs
5. Check for CORS errors in the console
6. Verify that preflight OPTIONS requests are successful

## Common Issues

1. **Missing OPTIONS method**: Ensure your backend handles OPTIONS requests for preflight checks
2. **Incorrect origin**: Make sure the exact origin (including protocol and port) is allowed
3. **Missing headers**: Ensure all headers used by the frontend are allowed
4. **Credentials mismatch**: Frontend and backend credentials settings must match

## Security Considerations

1. **Production**: Only allow your actual production domains, not wildcards
2. **Development**: Restrict to localhost origins only
3. **Headers**: Only allow headers that are actually needed
4. **Methods**: Only allow HTTP methods that your API actually uses

## Frontend Configuration

The frontend is configured to:
- Use `withCredentials: false` by default
- Provide helpful error messages for CORS issues
- Allow easy configuration via environment variables

To change the API URL, create a `.env` file:
```
VITE_API_BASE_URL=http://your-backend-url/api/v1
```