# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a React + TypeScript + Vite application called "impact-resume-ui". It's currently a fresh Vite template with minimal setup and appears to be intended for building a resume/CV user interface.

## Tech Stack

- **Framework**: React 19.1.0 with TypeScript
- **Build Tool**: Vite 7.0.0
- **Linting**: ESLint 9.29.0 with TypeScript ESLint
- **Styling**: CSS (standard CSS files), Tailwind CSS (via @tailwindcss/vite plugin), ShadCN UI components

## Library Integration Guidelines

**IMPORTANT**: When adding new libraries or tools to this project, always prefer Vite-based integrations when available:

- Look for official Vite plugins first (e.g., `@library/vite` packages)
- Use Vite's plugin system rather than PostCSS, Webpack, or other build tool configurations
- Check the library's documentation for Vite-specific installation instructions
- This ensures optimal integration with our Vite build process and development server

Examples:
- ✅ Tailwind CSS: Use `@tailwindcss/vite` plugin
- ✅ Testing: Use `@vitest/ui` for Vitest integration
- ❌ Avoid: PostCSS-based integrations when Vite plugins exist

## Common Commands

```bash
# Development server with hot module replacement
npm run dev

# Build for production
npm run build

# Lint code
npm run lint

# Preview production build
npm run preview
```

## Architecture

The application follows a standard Vite + React structure:

- `src/main.tsx` - Application entry point using React 19's createRoot
- `src/App.tsx` - Main application component (currently default Vite template)
- `src/index.css` - Global styles
- `src/App.css` - App-specific styles
- `public/` - Static assets
- `index.html` - HTML template

## Build Process

The build process uses TypeScript compilation followed by Vite bundling:
1. `tsc -b` - TypeScript compilation
2. `vite build` - Vite bundling and optimization

## ESLint Configuration

The project uses modern ESLint flat config with:
- TypeScript ESLint recommended rules
- React Hooks plugin
- React Refresh plugin for Vite
- Targets browser environment
- Ignores `dist` directory

## TypeScript Configuration

Uses project references with separate configs:
- `tsconfig.json` - Root configuration with references
- `tsconfig.app.json` - App-specific TypeScript config
- `tsconfig.node.json` - Node.js/build tool config

## UI Component Guidelines

**CRITICAL**: This project uses ShadCN UI as the primary component library. When building UI components, ALWAYS follow these guidelines:

### ShadCN Component Usage
- **ALWAYS use ShadCN components first**: Before creating custom UI components or using other libraries, check if ShadCN has the component available
- **Import path**: Use the configured alias `@/components/ui/[component-name]` for all ShadCN imports
- **Adding new ShadCN components**: Use `npx shadcn@latest add [component-name]` to add new components
- **Available components**: Check https://ui.shadcn.com/docs/components for the full component library

### Component Priority Order
1. **First choice**: ShadCN UI components (`@/components/ui/*`)
2. **Second choice**: Extend/customize existing ShadCN components
3. **Last resort**: Create completely custom components (only when ShadCN doesn't have a suitable option)

### Examples
```tsx
// ✅ PREFERRED: Use ShadCN components
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

// ✅ ACCEPTABLE: Extend ShadCN components
import { Button } from "@/components/ui/button"
const CustomButton = ({ children, ...props }) => (
  <Button className="custom-styling" {...props}>{children}</Button>
)

// ❌ AVOID: Creating custom UI components when ShadCN equivalents exist
const CustomButton = () => <button className="...">...</button>
```

### ShadCN Configuration
- **Components path**: `src/components/ui/`
- **Utils path**: `src/lib/utils.ts` (contains `cn()` utility for class merging)
- **Design system**: Uses CSS variables for theming (light/dark mode support)
- **Icon library**: Lucide React (pre-configured with ShadCN)